{"$schema": "./node_modules/@types/node/package.json", "name": "sepjo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "node prisma/seed.js", "postinstall": "prisma generate"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "24.0.14", "@types/react": "19.1.8", "eslint": "^9", "eslint-config-next": "15.3.5", "prisma": "^6.12.0", "tailwindcss": "^4", "typescript": "5.8.3"}}